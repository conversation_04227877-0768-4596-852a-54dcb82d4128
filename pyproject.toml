[project]
name = "orga-ai"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aerich[toml]>=0.9.0",
    "tortoise-orm[accel]>=0.25.1",
    "ulid>=1.1",
    "python-dotenv",
    "asyncpg",
    "orjson>=3.9.15",
    "fastapi>=0.115.12",
    "uvicorn[standard]>=0.34.3",
    "python-multipart>=0.0.20",
    "pyjwt>=2.10.1",
    "langchain[openai]>=0.3.25",
    "langgraph>=0.4.8",
    "lmnr[all]>=0.6.10",
]

[tool.aerich]
tortoise_orm = "src.database.TORTOISE_ORM"
location = "./src/database/migrations"
src_folder = "./."

[dependency-groups]
dev = ["pytest>=8.4.0", "pytest-asyncio>=1.0.0", "httpx>=0.27.2"]

[tool.pytest.ini_options]
pythonpath = ["."]
asyncio_mode = "auto"
filterwarnings = ["error", "ignore::DeprecationWarning", "ignore::UserWarning"]
norecursedirs = ["./tests/__pycache__"]
